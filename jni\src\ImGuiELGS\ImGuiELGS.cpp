#include "ImGuiELGS.h"
#include "font_1.h"
#include "font_2.h"
#include <unordered_set>
#include <map>
#include <string>
#include <android/log.h>
#define LOG_TAG "MyApp" // 定义日志标签
#define LOGI(...) ((void)__android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__))
float dx = 20.f, yuan, zuo, you;
float ycsz, ycsd, ycsp;
float xyd;
bool show556, show762, showRifle, showSubmachine, showSniper, showMirror, showExpansion, showOtherParts, showDrug, showArmor, 空投, 骨灰盒, 地铁;
bool 透明 = false, 观透 = false, 首次 = false;
bool aigl = false, dynamic;
bool 类名 = false, 无敌炫酷;
int 血条 = 0;
int ArraysCount;
int OwnTeam = 0;
uintptr_t 基址头;
std::unordered_map<int, ImTextureID> 手持图片;
double static_ratio = 0.2;
float Fov;
int SelfAction = 0, 自身动作 = 0, 自身武器 = 0;
int bIsGunADS = 0, IsFire = 0;
long int WorldAddress, ArrayAddress, SelfAddress, Controller, CameraAddress, RootPoint;
extern int output;
int SkeletonList_New[][2]{{5, 4}, {4, 0}, {4, 11}, {11, 12}, {12, 13}, {4, 33}, {33, 34}, {34, 35}, {0, 55}, {55, 56}, {56, 57}, {0, 59}, {59, 60}, {60, 61}};
int SkeletonList_Old[][2]{{5, 4}, {4, 0}, {4, 11}, {11, 12}, {12, 63}, {4, 32}, {32, 33}, {33, 62}, {0, 52}, {52, 53}, {53, 54}, {0, 56}, {56, 57}, {57, 58}};
int SkeletonList_Yan[][2]{{5, 4}, {4, 0}, {4, 13}, {13, 14}, {14, 15}, {4, 35}, {35, 36}, {36, 37}, {0, 57}, {57, 58}, {58, 59}, {0, 61}, {61, 62}, {62, 63}};
int SkeletonList_S30[][2]{{5, 4}, {4, 0}, {5, 6}, {6, 7}, {7, 8}, {5, 34}, {34, 35}, {35, 36}, {0, 55}, {55, 56}, {56, 57}, {0, 59}, {59, 60}, {60, 61}};
std::unordered_map<int, std::unordered_map<std::string, std::string>> itemTypeMap = {
    {1, {// 投掷物类
         {"ojGrenade_BP_C", "手雷"},
         {"BP_AirraidActor_C", "空袭"},
         {"BP_TraceGrenade", "追踪雷"},
         {"BP_WEP_DragonBoySpear_C", "雷枪"},
         {"BuildingActor_ConcertStage_MusicGirl_BP_C", "狗窝"},
         {"ojBurn_BP_C", "燃烧弹"}}},
    {2, {// 载具类
         {"_Mountainbike_Training_C", "自行车"},
         {"VH_HammerShark_C", "小鲨鱼"},
         {"Mirado", "双人跑车"},
         {"Scooter", "小绵羊"},
         {"VH_Horse", "马"},
         {"_BRDM_C", "装甲车"},
         {"VH_Motorcycle_C", "摩托车"},
         {"Snowmobile", "雪地摩托"},
         {"StationWagon", "旅行车"},
         {"BP_VH_Buggy", "蹦蹦车"},
         {"VH_Dacia_", "轿车"},
         {"VH_UAZ01_New_C", "吉普车"},
         {"PickUp_07_C", "皮卡车"},
         {"CoupeRB", "双人跑车"},
         {"_MiniBus_01_C", "迷你巴士"},
         {"_PG117_C", "快艇"},
         {"uaRail_1_C", "摩托艇"},
         {"_Motorglider_C", "滑翔机"},
         {"BP_VH_Bigfoot_C", "大脚车"},
         {"VH_ATV1_C", "四轮摩托"},
         {"Rony_01_C", "行李车"},
         {"VH_UTV_C", "越野车"},
         {"BP_VH_Tuk_1_C", "三轮车"},
         {"VH_Snowmobile_C", "雪橇车"},
         {"PG117", "船"},
         {"VH_4SportCar_C", "跑车"},
         {"BP_Excavator_C", "挖掘机"},
         {"VH_Kite_C", "风筝"},
         {"VH_Drift", "拉力赛车"},
         {"VH_Blanc_C", "SUV电车"},
         {"VH_Picobus_C", "大巴车"},
         {"VH_DumpTruck_C", "泥土车"},
         {"VH_Excavator_C", "挖掘机"},
         {"HugeMouthJet_RPG_C", "战斗机"},
         {"VH_LostMobile_C", "霹雳车"},
         {"VH_DesertCar_C", "沙漠越野车"}}},
    {3, {// 防具类
         {"BP_Armor_Lv3_C", "三级甲"},
         {"Bag_Lv3", "三级包"},
         {"BP_Helmet_Lv3_C", "三级头"}}},
    {4, {// 道具类
         {"BP_Pistol_RevivalFlaregun", "召回信号枪"},
         {"BP_Ammo_RevivalFlare_Pickup_C", "召回信号弹"},
         {"BP_Ammo_Flare_Pickup_C", "信号弹"},
         {"MilitarySupplyBoxBase_Baltic", "主题箱子"},
         {"MilitarySupp", "主题箱子"},
         {"CG027_Lottery_C", "扭蛋机"},
         {"Pistol_Flaregun", "信号枪"},
         {"revivalAED_Pickup_C", "自救器"},
         {"BP_Neon_Coin_Pickup_C", "钱币"},
         {"BP_Pickup_Finger_C", "钩锁"},
         {"AirDropBox_C", "空投"},
         {"BP_Grenade_EmergencyCall_Weapon_Wrapper_C", "紧急呼救器"},
         {"BP_Other_Mortar_Bullet_C", "注意迫击炮"},
         {"BP_AirDropBox_SuperPeople_C", "空投"},
         {"AirDropListWrapperActor", "空投"},
         {"BP_AirdropChipBox_C", "金仓"},
         {"CG030_Market_SafeBox_C", "保险"},
         {"CG030_Market_SafeBox_2_C", "保险"},
         {"perPeopleSkill", "金插"}}},
    {5, {// 盒子
         {"_PlayerDeadListWrapper_C", "盒子"}}},
    {6, {// 药品
         {"ink_Pickup_C", "饮料"},
         {"lls_Pickup_C", "止痛药"},
         {"jection_Pickup_C", "肾上腺素"},
         {"rstAidbox_Pickup_C", "医疗箱"}}},
    {7, {// 子弹
         {"Ammo_556", "556子弹"},
         {"Ammo_9mm", "9mm子弹"},
         {"Ammo_45AC", "45子弹"},
         {"Ammo_762", "762子弹"}}},
    {8, {// 762枪械
         {"BP_Rifle_AKM_Wrapper_C", "AKM"},
         {"BP_Rifle_ACE32_Wrapper_C", "ACE32"},
         {"BP_Rifle_HoneyBadger_Wrapper_C", "蜜獾"},
         {"BP_Rifle_ARX200_CWrapper_C", "ARX200"},
         {"BP_Rifle_M762_Wrapper_C", "M762"},
         {"BP_Rifle_Groza_Wrapper_C", "Groza"},
         {"BP_Other_PKM_Wrapper_C", "PKM"}}},
    {9, {// 556枪械
         {"BP_Rifle_SCAR_Wrapper_C", "SCAR"},
         {"BP_Rifle_M416_Wrapper_C", "M416"},
         {"BP_Rifle_G36_Wrapper_C", "G36"},
         {"BP_Rifle_Famas_Wrapper_C", "Famas"},
         {"BP_Other_MG36_Wrapper_C", "MG36"}}},

    {10, {// 冲锋枪
          {"BP_MachineGun_UMP9_Wrapper_C", "UMP45"},
          {"BP_MachineGun_JS9_Wrapper_C", "JS9"},
          {"BP_MachineGun_AKS74U_Wrapper_C", "AKS74U"},
          {"BP_MachineGun_Vector_Wrapper_C", "Vector"},
          {"QK_Mid_Suppressor", "冲消"},
          {"DJ_Mid_EQ", "冲锋快扩"}}},

    {11, {// 霰弹枪
          {"BP_ShotGun_S12K_Wrapper_C", "S12K"},
          {"BP_ShotGun_DP12_Wrapper_C", "DBS"},
          {"BP_ShotGun_SPAS_Wrapper_C", "SPAS"},
          {"BP_ShotGun_AA12_Wrapper_C", "AA12"},
          {"Ammo_12Guage", "喷子子弹"},
          {"BP_QK_Choke_Pickup", "收束器"},
          {"BP_QK_DuckBill_Pickup", "鸭嘴"},
          {"BP_DJ_ShotGun_Pickup_C", "散弹快速"}}},

    {12, {// 狙击枪
          {"BP_Sniper_SVD_Wrapper_C", "SVD"},
          {"BP_Sniper_SKS_Wrapper_C", "SKS"},
          {"BP_Rifle_M417_Wrapper_C", "M417"},
          {"BP_Sniper_M1Garand_CWrapper_C", "M!加兰德"},
          {"BP_Sniper_Mini14_Wrapper_C", "Mini14"},
          {"DJ_Sniper_EQ", "狙击快扩"},
          {"BP_QT_Sniper_Pickup", "狙击枪托"},
          {"QK_Sniper_Suppressor", "狙消"}}},

    {13, {// 其他
          {"BP_Other_Mortar_Wrapper_C", "迫击炮"},
          {"Ammo_Bolt", "箭"},
          {"BP_Other_HuntingBow_Wrapper_C", "爆炸弓"}}},

    {14, {// 步枪配件
          {"DJ_Large_EQ_Pickup_C", "步枪快扩"},
          {"BP_QT_A_Pickup", "步枪枪托"},
          {"BP_WB_LightGrip_Pickup_C", "轻型"},
          {"BP_WB_Lasersight_Pickup_C", "激光"},
          {"BP_WB_Angled_Pickup_C", "直角"},
          {"QK_Large_Suppressor", "步消"}}},

    {15, {// 倍镜
          {"MZJ_8X_Pickup_C", "8倍"},
          {"MZJ_6X_Pickup_C", "6倍"}}},

    {16, {// 地铁宝箱
          {"EscapeBox_SupplyBox_", "物资箱"},
          {"EscapeBoxHight_SupplyBox_", "物资箱"}}}};
bool isPartialMatchedType(int type, const std::string &itemName, std::string &matchedName)
{
    auto it = itemTypeMap.find(type);
    if (it != itemTypeMap.end())
    {
        auto &subMap = it->second;
        for (const auto &entry : subMap)
        {
            if (itemName.find(entry.first) != std::string::npos)
            {
                matchedName = entry.second;
                return true;
            }
        }
    }
    return false;
}
// 地铁武器映射
const std::unordered_map<int, std::unordered_set<int>> metroWeaponToBaseWeaponMap = {
    {102003, {9811015, 9811057, 9811058, 9811021, 9811020, 9811019}},
    {102007, {9811036, 9811040, 9811041, 9811042}},
    {102008, {9811043, 9811047, 9811048, 9811049}},
    {102105, {9811050, 9811061, 9811062, 9811054, 9811055, 9811056}},
    {101001, {9812002, 9812006, 9812007, 9812008}},
    {101003, {9812015, 9812019, 9812020, 9812021}},
    {101004, {9812023, 9812027, 9812028, 9812029, 9812092}},
    {101005, {9812029, 9812099, 9812100, 9812032, 9812033, 9812034}},
    {101006, {9812036, 9812101, 9812102, 9812040, 9812041, 9812042}},
    {101007, {9812043, 9812103, 9812104, 9812047, 9812048, 9812049}},
    {101008, {9812050, 9812105, 9812106, 9812054, 9812055, 9812056}},
    {101010, {9812064, 9812068, 9812069, 9812070}},
    {101012, {9812078, 9812109, 9812110, 9812082, 9812083, 9812084}},
    {101013, {9812085, 9812089, 9812090, 9812091}},
    {105001, {9813001, 9813035, 9813036, 9813004, 9813005, 9813006}},
    {105010, {9813022, 9813037, 9813038, 9813025, 9813026, 9813027}},
    {105012, {9813029, 9813039, 9813040, 9813032, 9813033, 9813034}},
    {103002, {9814008}},
    {103003, {9814015, 9814060, 9814061}},
    {103012, {9814036, 9814062, 9814063}},
    {103015, {9814043, 9814064, 9814065}},
    {103004, {9815001, 9815072, 9815073}},
    {103007, {9815022, 9815074, 9815075}},
    {103009, {9815029, 9815076, 9815077}},
    {103013, {9815043, 9815078, 9815079}},
    {103014, {9815050, 9815080, 9815081}},
    {103100, {9815064, 9815082, 9815083}}};
void ImGuiELGS::WeaponPressureGunValue(int 自身武器, float Fov, bool &isShotgun, bool &isSniper, float &standCrouchPressure, float &pronePressure)
{
    isShotgun = false;
    isSniper = false;
    standCrouchPressure = 0.99f;
    pronePressure = 0.95f;
    const std::unordered_set<int> shotgunWeaponIDs = {104001, 104002, 104003, 104004, 104100, 104101, 104102};
    const std::unordered_set<int> sniperWeaponIDs = {103001, 103002, 103003, 103004, 103006, 103009, 103011, 103012, 103013, 103014, 103015, 103016, 103100, 103903};
    const float epsilon = 0.01f;

    // FOV 力度索引映射，根据FOV获取yq数组的索引
    int fovPressureIndex = [&]() -> int
    {
        if (Fov > 75 && Fov <= 130)
            return 23; // 腰射
        if (Fov == 70 || Fov == 75)
            return 24; // 机瞄
        if (Fov == 55 || Fov == 60)
            return 25; // 红点
        if ((int)Fov == 44)
            return 26; // 二倍
        if ((int)Fov == 26)
            return 27; // 三倍
        if ((int)Fov == 20)
            return 28; // 四倍
        if ((int)Fov == 13)
            return 29; // 六倍
        if (std::abs(Fov - 11.03f) < epsilon)
            return 30; // 八倍
        return -1;
    }();

    // 获取FOV对应的压枪系数
    float fovPressureValue = (fovPressureIndex != -1) ? imguiswitch_information.floatswitch[fovPressureIndex] : 1.0f;
    // 地铁武器映射
    for (const auto &entry : metroWeaponToBaseWeaponMap)
    {
        if (entry.second.find(自身武器) != entry.second.end())
        {
            自身武器 = entry.first;
            break;
        }
    }

    // 武器类型检查
    isShotgun = shotgunWeaponIDs.count(自身武器);
    isSniper = sniperWeaponIDs.count(自身武器);

    // 武器索引映射（站蹲和趴下使用相同索引）
    static const std::unordered_map<int, int> weaponIndexMap = {
        {101001, 0},  // AKM
        {101002, 1},  // M16A4
        {101003, 2},  // SCAR-L
        {101004, 3},  // M416
        {101005, 4},  // Groza
        {101006, 5},  // AUG
        {101007, 6},  // QBZ
        {101008, 7},  // M762
        {101009, 8},  // Mk47
        {101010, 9},  // G36C
        {101011, 10}, // AC-VAL
        {101012, 11}, // 蜜獾突击步枪
        {101013, 12}, // 法玛斯
        {102001, 13}, // UZI
        {102002, 14}, // UMP 5
        {102003, 15}, // Vector
        {102004, 16}, // 汤姆逊
        {102005, 17}, // 野牛
        {102007, 18}, // MP5K
        {102105, 19}, // P90
        {105001, 20}, // M249
        {105002, 21}, // DP-28
        {105010, 22}, // MG3
        {105012, 23}, // PKM
        {105013, 24}, // MG-36
        {102008, 25}, // AKS
        {103007, 26}, // MK14
        {101014, 27}, // ACE32
        {102009, 28}, // JS9
        {103006, 29}, // mini4
        {103009, 30}, // slr
        {103004, 31}, // sks
        {103100, 32}, // mk12
        {103013, 33}, // m417
        {103014, 34}, // mk20
        {103010, 35}  // QBU
    };

    auto it = weaponIndexMap.find(自身武器);
    if (it != weaponIndexMap.end() && fovPressureIndex != -1)
    {
        int weaponIndex = it->second;

        // 使用预先获取的yq值计算压枪力度
        standCrouchPressure = imguiswitch_information.StandingRecoil[weaponIndex] * fovPressureValue;
        pronePressure = imguiswitch_information.ProneRecoil[weaponIndex] * fovPressureValue;
    }
}

ImColor tempColors[] = {
    ImColor(255, 0, 0, 255), // 红色
    ImColor(0, 255, 0, 255), // 绿色
    ImColor(0, 0, 255, 255), // 蓝色
    ImColor(0, 255, 0, 255),
    ImColor(0, 255, 0, 255),
    ImColor(0, 255, 0, 255),
    ImColor(0, 255, 0, 255),
    ImColor(0, 255, 0, 255),
    ImColor(0, 255, 0, 255)};

void miaobian(float size, int x, int y, ImVec4 color, const char *str)
{
    ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x + 1.0, y), ImGui::ColorConvertFloat4ToU32(ImVec4(0.0f, 0.0f, 0.0f, 1.0f)), str);
    ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x - 0.1, y), ImGui::ColorConvertFloat4ToU32(ImVec4(0.0f, 0.0f, 0.0f, 1.0f)), str);
    ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x, y + 1.0), ImGui::ColorConvertFloat4ToU32(ImVec4(0.0f, 0.0f, 0.0f, 1.0f)), str);
    ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x, y - 1.0), ImGui::ColorConvertFloat4ToU32(ImVec4(0.0f, 0.0f, 0.0f, 1.0f)), str);
    ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x, y), ImGui::ColorConvertFloat4ToU32(color), str);
}
void 绘制加粗文本(float size, float x, float y, ImColor color, ImColor color1, const char *str)
{
    ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x - 0.8, y - 0.8), color1, str);
    ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x + 0.8, y + 0.8), color1, str);
    ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x, y), color, str);
}

void DrawPlayerBox(ImDrawList *Draw, float left, float right, float bottom, float top, float x, float y, ImColor color, float size)
{
    float LineSize = size;
    // x距离，y距离
    float xd = x - left;
    float yd = y - top;

    // 左上角
    Draw->AddLine(ImVec2(left, top), ImVec2(left, top + yd / 3), color, LineSize);
    Draw->AddLine(ImVec2(left, top), ImVec2(left + xd / 2, top), color, LineSize);

    // 右上角
    Draw->AddLine(ImVec2(right, top), ImVec2(right, top + yd / 3), color, LineSize);
    Draw->AddLine(ImVec2(right, top), ImVec2(right - xd / 2, top), color, LineSize);

    // 左下角
    Draw->AddLine(ImVec2(left, bottom), ImVec2(left, bottom - yd / 3), color, LineSize);
    Draw->AddLine(ImVec2(left, bottom), ImVec2(left + xd / 2, bottom), color, LineSize);

    // 右下角
    Draw->AddLine(ImVec2(right, bottom), ImVec2(right, bottom - yd / 3), color, LineSize);
    Draw->AddLine(ImVec2(right, bottom), ImVec2(right - xd / 2, bottom), color, LineSize);
}
ImTextureID ImAgeHeadFile(const unsigned char *buf, int len)
{
    int w, h, n;
    stbi_uc *data = stbi_png_load_from_memory(buf, len, &w, &h, &n, 0);
    GLuint texture;
    glGenTextures(1, &texture);
    glEnable(GL_TEXTURE_2D);
    glBindTexture(GL_TEXTURE_2D, texture);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_NEAREST);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_NEAREST);
    if (n == 3)
    {
        glTexImage2D(GL_TEXTURE_2D, 0, GL_RGB, w, h, 0, GL_RGB, GL_UNSIGNED_BYTE, data);
    }
    else
    {
        glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, w, h, 0, GL_RGBA, GL_UNSIGNED_BYTE, data);
    }
    stbi_image_free(data);
    ImTextureID image_id = (ImTextureID)(GLuint *)texture;
    return image_id;
}

bool 获取枪械信息(int 枪械编码, ImTextureID *图片名称)
{
    auto it = 手持图片.find(枪械编码);
    if (it != 手持图片.end())
    {
        *图片名称 = it->second;
        return true;
    }
    return false;
}

void 加载图片()
{
    手持图片[101001] = ImAgeHeadFile(picture_101001_png, sizeof(picture_101001_png));
    手持图片[101002] = ImAgeHeadFile(picture_101002_png, sizeof(picture_101002_png));
    手持图片[101003] = ImAgeHeadFile(picture_101003_png, sizeof(picture_101003_png));
    手持图片[101004] = ImAgeHeadFile(picture_101004_png, sizeof(picture_101004_png));
    手持图片[101005] = ImAgeHeadFile(picture_101005_png, sizeof(picture_101005_png));
    手持图片[101006] = ImAgeHeadFile(picture_101006_png, sizeof(picture_101006_png));
    手持图片[101007] = ImAgeHeadFile(picture_101007_png, sizeof(picture_101007_png));
    手持图片[101008] = ImAgeHeadFile(picture_101008_png, sizeof(picture_101008_png));
    手持图片[101009] = ImAgeHeadFile(picture_101009_png, sizeof(picture_101009_png));
    手持图片[101010] = ImAgeHeadFile(picture_101010_png, sizeof(picture_101010_png));
    手持图片[101011] = ImAgeHeadFile(picture_101011_png, sizeof(picture_101011_png));
    手持图片[101012] = ImAgeHeadFile(picture_101012_png, sizeof(picture_101012_png));
    手持图片[101013] = ImAgeHeadFile(picture_101013_png, sizeof(picture_101013_png));
    手持图片[102001] = ImAgeHeadFile(picture_102001_png, sizeof(picture_102001_png));
    手持图片[102002] = ImAgeHeadFile(picture_102002_png, sizeof(picture_102002_png));
    手持图片[102003] = ImAgeHeadFile(picture_102003_png, sizeof(picture_102003_png));
    手持图片[102004] = ImAgeHeadFile(picture_102004_png, sizeof(picture_102004_png));
    手持图片[102005] = ImAgeHeadFile(picture_102005_png, sizeof(picture_102005_png));
    手持图片[102007] = ImAgeHeadFile(picture_102007_png, sizeof(picture_102007_png));
    手持图片[102008] = ImAgeHeadFile(picture_102008_png, sizeof(picture_102008_png));
    手持图片[102105] = ImAgeHeadFile(picture_102105_png, sizeof(picture_102105_png));
    手持图片[103001] = ImAgeHeadFile(picture_103001_png, sizeof(picture_103001_png));
    手持图片[103002] = ImAgeHeadFile(picture_103002_png, sizeof(picture_103002_png));
    手持图片[103003] = ImAgeHeadFile(picture_103003_png, sizeof(picture_103003_png));
    手持图片[103004] = ImAgeHeadFile(picture_103004_png, sizeof(picture_103004_png));
    手持图片[103005] = ImAgeHeadFile(picture_103005_png, sizeof(picture_103005_png));
    手持图片[103006] = ImAgeHeadFile(picture_103006_png, sizeof(picture_103006_png));
    手持图片[103007] = ImAgeHeadFile(picture_103007_png, sizeof(picture_103007_png));
    手持图片[103008] = ImAgeHeadFile(picture_103008_png, sizeof(picture_103008_png));
    手持图片[103009] = ImAgeHeadFile(picture_103009_png, sizeof(picture_103009_png));
    手持图片[103010] = ImAgeHeadFile(picture_103010_png, sizeof(picture_103010_png));
    手持图片[103011] = ImAgeHeadFile(picture_103011_png, sizeof(picture_103011_png));
    手持图片[103012] = ImAgeHeadFile(picture_103012_png, sizeof(picture_103012_png));
    手持图片[103013] = ImAgeHeadFile(picture_103013_png, sizeof(picture_103013_png));
    手持图片[103014] = ImAgeHeadFile(picture_103014_png, sizeof(picture_103014_png));
    手持图片[103015] = ImAgeHeadFile(picture_103015_png, sizeof(picture_103015_png));
    手持图片[103016] = ImAgeHeadFile(picture_103016_png, sizeof(picture_103016_png));
    手持图片[103100] = ImAgeHeadFile(picture_103100_png, sizeof(picture_103100_png));
    手持图片[103901] = ImAgeHeadFile(picture_103901_png, sizeof(picture_103901_png));
    手持图片[103902] = ImAgeHeadFile(picture_103902_png, sizeof(picture_103902_png));
    手持图片[103903] = ImAgeHeadFile(picture_103903_png, sizeof(picture_103903_png));
    手持图片[104001] = ImAgeHeadFile(picture_104001_png, sizeof(picture_104001_png));
    手持图片[104002] = ImAgeHeadFile(picture_104002_png, sizeof(picture_104002_png));
    手持图片[104003] = ImAgeHeadFile(picture_104003_png, sizeof(picture_104003_png));
    手持图片[104004] = ImAgeHeadFile(picture_104004_png, sizeof(picture_104004_png));
    手持图片[104005] = ImAgeHeadFile(picture_104005_png, sizeof(picture_104005_png));
    手持图片[104100] = ImAgeHeadFile(picture_104100_png, sizeof(picture_104100_png));
    手持图片[105001] = ImAgeHeadFile(picture_105001_png, sizeof(picture_105001_png));
    手持图片[105002] = ImAgeHeadFile(picture_105002_png, sizeof(picture_105002_png));
    手持图片[105010] = ImAgeHeadFile(picture_105010_png, sizeof(picture_105010_png));
    手持图片[105012] = ImAgeHeadFile(picture_105012_png, sizeof(picture_105012_png));
    手持图片[105013] = ImAgeHeadFile(picture_105013_png, sizeof(picture_105013_png));
    手持图片[106001] = ImAgeHeadFile(picture_106001_png, sizeof(picture_106001_png));
    手持图片[106002] = ImAgeHeadFile(picture_106002_png, sizeof(picture_106002_png));
    手持图片[106003] = ImAgeHeadFile(picture_106003_png, sizeof(picture_106003_png));
    手持图片[106004] = ImAgeHeadFile(picture_106004_png, sizeof(picture_106004_png));
    手持图片[106005] = ImAgeHeadFile(picture_106005_png, sizeof(picture_106005_png));
    手持图片[106006] = ImAgeHeadFile(picture_106006_png, sizeof(picture_106006_png));
    手持图片[106007] = ImAgeHeadFile(picture_106007_png, sizeof(picture_106007_png));
    手持图片[106008] = ImAgeHeadFile(picture_106008_png, sizeof(picture_106008_png));
    手持图片[106010] = ImAgeHeadFile(picture_106010_png, sizeof(picture_106010_png));
    手持图片[107001] = ImAgeHeadFile(picture_107001_png, sizeof(picture_107001_png));
    手持图片[107007] = ImAgeHeadFile(picture_107007_png, sizeof(picture_107007_png));
    手持图片[107010] = ImAgeHeadFile(picture_107010_png, sizeof(picture_107010_png));
    手持图片[107909] = ImAgeHeadFile(picture_107909_png, sizeof(picture_107909_png));
    手持图片[108001] = ImAgeHeadFile(picture_108001_png, sizeof(picture_108001_png));
    手持图片[108002] = ImAgeHeadFile(picture_108002_png, sizeof(picture_108002_png));
    手持图片[108003] = ImAgeHeadFile(picture_108003_png, sizeof(picture_108003_png));
    手持图片[108004] = ImAgeHeadFile(picture_108004_png, sizeof(picture_108004_png));
    手持图片[602001] = ImAgeHeadFile(picture_602001_png, sizeof(picture_602001_png));
    手持图片[602002] = ImAgeHeadFile(picture_602002_png, sizeof(picture_602002_png));
    手持图片[602003] = ImAgeHeadFile(picture_602003_png, sizeof(picture_602003_png));
    手持图片[602004] = ImAgeHeadFile(picture_602004_png, sizeof(picture_602004_png));
    手持图片[602075] = ImAgeHeadFile(picture_602075_png, sizeof(picture_602075_png));
    手持图片[107910] = ImAgeHeadFile(picture_107910_png, sizeof(picture_107910_png));

    手持图片[101014] = ImAgeHeadFile(picture_101014_png, sizeof(picture_101014_png));
    手持图片[102009] = ImAgeHeadFile(picture_102009_png, sizeof(picture_102009_png));
    手持图片[107100] = ImAgeHeadFile(picture_107100_png, sizeof(picture_107100_png));

    手持图片[106013] = ImAgeHeadFile(picture_106013_png, sizeof(picture_106013_png));
    手持图片[103101] = ImAgeHeadFile(picture_103101_png, sizeof(picture_103101_png));

    手持图片[107006] = ImAgeHeadFile(picture_107006_png, sizeof(picture_107006_png));
    手持图片[107008] = ImAgeHeadFile(picture_107008_png, sizeof(picture_107008_png));

    手持图片[103017] = ImAgeHeadFile(picture_103017_png, sizeof(picture_103017_png));
    手持图片[101016] = ImAgeHeadFile(picture_101016_png, sizeof(picture_101016_png));

    // 手持图片映射
    手持图片[9811015] = 手持图片[102003];
    手持图片[9811057] = 手持图片[102003];
    手持图片[9811058] = 手持图片[102003];
    手持图片[9812130] = 手持图片[101010];
    手持图片[9812125] = 手持图片[101004];
    手持图片[9812126] = 手持图片[101005];
    手持图片[9812127] = 手持图片[101006];
    手持图片[9812124] = 手持图片[101003];
    手持图片[9812131] = 手持图片[101012];
    手持图片[9812143] = 手持图片[101013];
    手持图片[9812129] = 手持图片[101008];
    手持图片[9815094] = 手持图片[103014];
    手持图片[9815091] = 手持图片[103007];
    手持图片[9815092] = 手持图片[103009];
    手持图片[9815090] = 手持图片[103004];
    手持图片[9811067] = 手持图片[102007];
    手持图片[9811068] = 手持图片[102105];
    手持图片[9811066] = 手持图片[102003];
    手持图片[9813056] = 手持图片[105013];
    手持图片[9813053] = 手持图片[105010];
    手持图片[9813054] = 手持图片[105012];
    手持图片[9814074] = 手持图片[103012];

    手持图片[9811036] = 手持图片[102007];

    手持图片[9811043] = 手持图片[102008];
    手持图片[9812143] = 手持图片[101014];

    手持图片[9811050] = 手持图片[102105];
    手持图片[9811061] = 手持图片[102105];
    手持图片[9811062] = 手持图片[102105];

    手持图片[9812002] = 手持图片[101001];
    手持图片[9812123] = 手持图片[101001];

    手持图片[9812015] = 手持图片[101003];

    手持图片[9812023] = 手持图片[101004];

    手持图片[9812029] = 手持图片[101005];
    手持图片[9812099] = 手持图片[101005];
    手持图片[9812100] = 手持图片[101005];

    手持图片[9812036] = 手持图片[101006];
    手持图片[9812101] = 手持图片[101006];
    手持图片[9812102] = 手持图片[101006];

    手持图片[9812043] = 手持图片[101007];
    手持图片[9812103] = 手持图片[101007];
    手持图片[9812104] = 手持图片[101007];

    手持图片[9812050] = 手持图片[101008];
    手持图片[9812105] = 手持图片[101008];
    手持图片[9812106] = 手持图片[101008];

    手持图片[9812064] = 手持图片[101010];
    手持图片[9812107] = 手持图片[101010];
    手持图片[9812108] = 手持图片[101010];

    手持图片[9812078] = 手持图片[101012];
    手持图片[9812109] = 手持图片[101012];
    手持图片[9812110] = 手持图片[101012];

    手持图片[9812085] = 手持图片[101013];
    手持图片[9812111] = 手持图片[101013];
    手持图片[9812112] = 手持图片[101013];

    手持图片[9813001] = 手持图片[105001];
    手持图片[9813035] = 手持图片[105001];
    手持图片[9813036] = 手持图片[105001];

    手持图片[9813022] = 手持图片[105010];
    手持图片[9813037] = 手持图片[105010];
    手持图片[9813038] = 手持图片[105010];

    手持图片[9813029] = 手持图片[105012];
    手持图片[9813039] = 手持图片[105012];
    手持图片[9813040] = 手持图片[105012];

    手持图片[9814008] = 手持图片[103002];

    手持图片[9814015] = 手持图片[103003];
    手持图片[9814060] = 手持图片[103003];
    手持图片[9814061] = 手持图片[103003];

    手持图片[9814036] = 手持图片[103012];
    手持图片[9814062] = 手持图片[103012];
    手持图片[9814063] = 手持图片[103012];
    手持图片[103904] = 手持图片[103012];

    手持图片[9814043] = 手持图片[103015];
    手持图片[9814064] = 手持图片[103015];
    手持图片[9814065] = 手持图片[103015];

    手持图片[9815001] = 手持图片[103004];
    手持图片[9815072] = 手持图片[103004];
    手持图片[9815073] = 手持图片[103004];

    手持图片[9815022] = 手持图片[103007];
    手持图片[9815074] = 手持图片[103007];
    手持图片[9815075] = 手持图片[103007];

    手持图片[9815029] = 手持图片[103009];
    手持图片[9815076] = 手持图片[103009];
    手持图片[9815077] = 手持图片[103009];

    手持图片[9815043] = 手持图片[103013];
    手持图片[9815078] = 手持图片[103013];
    手持图片[9815079] = 手持图片[103013];

    手持图片[9815050] = 手持图片[103014];
    手持图片[9815080] = 手持图片[103014];
    手持图片[9815081] = 手持图片[103014];

    手持图片[9815064] = 手持图片[103100];
    手持图片[9815082] = 手持图片[103100];
    手持图片[9815083] = 手持图片[103100];
    手持图片[9825001] = 手持图片[602001];
    手持图片[9825002] = 手持图片[602002];
    手持图片[9825003] = 手持图片[602003];
    手持图片[9825004] = 手持图片[602004];
    手持图片[9826001] = 手持图片[602004];
    手持图片[9826002] = 手持图片[602004];
    手持图片[9810015] = 手持图片[104003];
    手持图片[9810025] = 手持图片[104004];
    手持图片[9810032] = 手持图片[104100];
    手持图片[9810043] = 手持图片[104005];
    手持图片[602104] = 手持图片[108001];
}
void ImGuiELGS::InitializeFoundationConfiGuration()
{
    OneTimeFrame = 89;
    touch_information.TouchRadius = 0.01f;
    touch_information.TouchPoints.x = 0.8f;
    touch_information.TouchPoints.y = 0.6f;

    touch_information.floatswitch[0] = 500.f;
    touch_information.floatswitch[1] = 0.05f;
    touch_information.floatswitch[2] = 0.10f;
    touch_information.floatswitch[3] = 0.10f;
    imguiswitch_information.intswitch[0] = 0;
    imguiswitch_information.intswitch[1] = 0;
    imguiswitch_information.intswitch[2] = 1;
    imguiswitch_information.intswitch[3] = 0;
    imguiswitch_information.intswitch[4] = 0;
    imguiswitch_information.intswitch[5] = 0;
    imguiswitch_information.intswitch[6] = 20;     // 物资大小
    imguiswitch_information.floatswitch[0] = 2.0f; // 画笔粗细
    imguiswitch_information.floatswitch[1] = -493.f;
    imguiswitch_information.floatswitch[2] = -126.f;
    imguiswitch_information.floatswitch[3] = 100.f;
    imguiswitch_information.floatswitch[4] = 134.12f;
    imguiswitch_information.floatswitch[5] = 100.f;  // 自瞄范围
    imguiswitch_information.floatswitch[6] = 200.f;  // 自瞄距离
    imguiswitch_information.floatswitch[7] = 1.2f;   // 自瞄预判
    imguiswitch_information.floatswitch[8] = 100.f;  // 自瞄血量
    imguiswitch_information.floatswitch[9] = 50;     // 自瞄最小范围
    imguiswitch_information.floatswitch[10] = 100.f; // 自瞄最大范围
    imguiswitch_information.floatswitch[11] = 50.f;  // 自瞄腰射距离
    imguiswitch_information.floatswitch[12] = 1.5f;  // 近战预判

    imguiswitch_information.floatswitch[20] = 1.6f; // 站着
    imguiswitch_information.floatswitch[21] = 1.5f; // 蹲着
    imguiswitch_information.floatswitch[22] = 0.7f; // 趴着

    imguiswitch_information.floatswitch[23] = 1.0f; // 腰射
    imguiswitch_information.floatswitch[24] = 1.5f; // 机瞄
    imguiswitch_information.floatswitch[25] = 1.5f; // 红点
    imguiswitch_information.floatswitch[26] = 1.5f; // 二倍
    imguiswitch_information.floatswitch[27] = 1.5f; // 三倍
    imguiswitch_information.floatswitch[28] = 1.5f; // 四倍
    imguiswitch_information.floatswitch[29] = 1.5f; // 六倍
    imguiswitch_information.floatswitch[30] = 1.5f; // 八倍

    imguiswitch_information.StandingRecoil[0] = 0.9f;  // AKM
    imguiswitch_information.StandingRecoil[1] = 0.6f;  // M16A4
    imguiswitch_information.StandingRecoil[2] = 0.7f;  // SCAR-L
    imguiswitch_information.StandingRecoil[3] = 0.7f;  // M416
    imguiswitch_information.StandingRecoil[4] = 0.8f;  // Groza
    imguiswitch_information.StandingRecoil[5] = 0.6f;  // AUG
    imguiswitch_information.StandingRecoil[6] = 0.6f;  // QBZ
    imguiswitch_information.StandingRecoil[7] = 0.8f;  // M762
    imguiswitch_information.StandingRecoil[8] = 0.9f;  // Mk47
    imguiswitch_information.StandingRecoil[9] = 0.5f;  // G36C
    imguiswitch_information.StandingRecoil[10] = 0.3f; // AC-VAL
    imguiswitch_information.StandingRecoil[11] = 0.8f; // 蜜獾突击步枪
    imguiswitch_information.StandingRecoil[12] = 0.3f; // 法玛斯
    imguiswitch_information.StandingRecoil[13] = 0.4f; // UZI
    imguiswitch_information.StandingRecoil[14] = 0.3f; // UMP 5
    imguiswitch_information.StandingRecoil[15] = 0.4f; // Vector
    imguiswitch_information.StandingRecoil[16] = 0.5f; // 汤姆逊
    imguiswitch_information.StandingRecoil[17] = 0.3f; // 野牛
    imguiswitch_information.StandingRecoil[18] = 0.4f; // MP5K
    imguiswitch_information.StandingRecoil[19] = 0.3f; // P90
    imguiswitch_information.StandingRecoil[20] = 0.5f; // M249
    imguiswitch_information.StandingRecoil[21] = 0.3f; // DP-28
    imguiswitch_information.StandingRecoil[22] = 0.3f; // MG3
    imguiswitch_information.StandingRecoil[23] = 0.3f; // PKM
    imguiswitch_information.StandingRecoil[24] = 0.2f; // MG-36
    imguiswitch_information.StandingRecoil[25] = 0.6f; // AKS
    imguiswitch_information.StandingRecoil[26] = 0.9f; // MK14
    imguiswitch_information.StandingRecoil[27] = 0.7f; // ACE32
    imguiswitch_information.StandingRecoil[28] = 0.4f; // JS9
    imguiswitch_information.StandingRecoil[29] = 0.9f; // mini4
    imguiswitch_information.StandingRecoil[30] = 1.4f; // slr
    imguiswitch_information.StandingRecoil[31] = 0.9f; // sks
    imguiswitch_information.StandingRecoil[32] = 0.8f; // mk12
    imguiswitch_information.StandingRecoil[33] = 0.2f; // m417
    imguiswitch_information.StandingRecoil[34] = 0.3;  // mk20
    imguiswitch_information.StandingRecoil[35] = 0.9f; // QBU

    imguiswitch_information.ProneRecoil[0] = 0.7f;  // AKM
    imguiswitch_information.ProneRecoil[1] = 0.7f;  // M16A4
    imguiswitch_information.ProneRecoil[2] = 0.5f;  // SCAR-L
    imguiswitch_information.ProneRecoil[3] = 0.5f;  // M416
    imguiswitch_information.ProneRecoil[4] = 1.5f;  // Groza
    imguiswitch_information.ProneRecoil[5] = 1.1f;  // AUG
    imguiswitch_information.ProneRecoil[6] = 1.3f;  // QBZ
    imguiswitch_information.ProneRecoil[7] = 1.6f;  // M762
    imguiswitch_information.ProneRecoil[8] = 1.4f;  // Mk47
    imguiswitch_information.ProneRecoil[9] = 0.6f;  // G36C
    imguiswitch_information.ProneRecoil[10] = 0.3f; // AC-VAL
    imguiswitch_information.ProneRecoil[11] = 1.6f; // 蜜獾突击步枪
    imguiswitch_information.ProneRecoil[12] = 0.5f; // 法玛斯
    imguiswitch_information.ProneRecoil[13] = 0.4f; // UZI
    imguiswitch_information.ProneRecoil[14] = 0.3f; // UMP 5
    imguiswitch_information.ProneRecoil[15] = 0.5f; // Vector
    imguiswitch_information.ProneRecoil[16] = 0.6f; // 汤姆逊
    imguiswitch_information.ProneRecoil[17] = 0.4f; // 野牛
    imguiswitch_information.ProneRecoil[18] = 0.5f; // MP5K
    imguiswitch_information.ProneRecoil[19] = 0.3f; // P90
    imguiswitch_information.ProneRecoil[20] = 0.9f; // M249
    imguiswitch_information.ProneRecoil[21] = 0.4f; // DP-28
    imguiswitch_information.ProneRecoil[22] = 0.4f; // MG3
    imguiswitch_information.ProneRecoil[23] = 0.5f; // PKM
    imguiswitch_information.ProneRecoil[24] = 0.3f; // MG-36
    imguiswitch_information.ProneRecoil[25] = 1.1f; // AKS
    imguiswitch_information.ProneRecoil[26] = 0.8f; // MK14
    imguiswitch_information.ProneRecoil[27] = 1.3f; // ACE32
    imguiswitch_information.ProneRecoil[28] = 0.9f; // JS9
    imguiswitch_information.ProneRecoil[29] = 1.0f; // mini4
    imguiswitch_information.ProneRecoil[30] = 1.9f; // slr
    imguiswitch_information.ProneRecoil[31] = 0.3f; // sks
    imguiswitch_information.ProneRecoil[32] = 0.5f; // mk12
    imguiswitch_information.ProneRecoil[33] = 0.2f; // m417
    imguiswitch_information.ProneRecoil[34] = 0.2f; // mk20
    imguiswitch_information.ProneRecoil[35] = 1.4f; // QBU

    imguiswitch_information.floatswitch[56] = 500.f;
    imguiswitch_information.floatswitch[57] = 1.70f;
    imguiswitch_information.colorswitch[0] = ImColor(0, 200, 0, 255);     // 方框颜色
    imguiswitch_information.colorswitch[1] = ImColor(0, 200, 0, 255);     // 骨骼颜色
    imguiswitch_information.colorswitch[2] = ImColor(240, 240, 0, 255);   // 射线颜色
    imguiswitch_information.colorswitch[3] = ImColor(240, 240, 240, 255); // 距离颜色
    imguiswitch_information.colorswitch[4] = ImColor(240, 240, 240, 255); // 名称颜色
    imguiswitch_information.colorswitch[5] = ImColor(255, 223, 0, 255);   // 队标颜色
    imguiswitch_information.colorswitch[6] = ImColor(200, 0, 0, 200);     // 人机颜色
    imguiswitch_information.colorswitch[7] = ImColor(200, 0, 0, 200);     // 瞄准颜色

    imguiswitch_information.colorswitch[8] = ImColor(255, 0, 0, 242);      // 投掷颜色 - 纯红（危险色）
    imguiswitch_information.colorswitch[9] = ImColor(0, 255, 0, 242);      // 载具颜色 - 纯绿（移动相关）
    imguiswitch_information.colorswitch[10] = ImColor(0, 204, 204, 255);   // 防具颜色 - 青蓝色（护甲联想）
    imguiswitch_information.colorswitch[11] = ImColor(255, 255, 0, 242);   // 道具颜色 - 纯黄（警告色）
    imguiswitch_information.colorswitch[12] = ImColor(255, 102, 178, 255); // 盒子颜色 - 玫红色（醒目且不冲突）
    imguiswitch_information.colorswitch[13] = ImColor(51, 204, 102, 255);  // 药品颜色 - 薄荷绿（生命恢复联想）
    imguiswitch_information.colorswitch[14] = ImColor(255, 153, 0, 255);   // 子弹颜色 - 橙黄色（弹药通用色）
    imguiswitch_information.colorswitch[15] = ImColor(153, 0, 204, 255);   // 762枪械 - 深紫色（与红色完全无关）
    imguiswitch_information.colorswitch[16] = ImColor(0, 153, 255, 255);   // 556枪械 - 钴蓝色（冷色调区分）
    imguiswitch_information.colorswitch[17] = ImColor(204, 0, 102, 255);   // 冲锋枪 - 酒红色（暖色但非红/绿）
    imguiswitch_information.colorswitch[18] = ImColor(230, 128, 0, 255);   // 霰弹枪 - 棕橙色（近战武器联想）
    imguiswitch_information.colorswitch[19] = ImColor(0, 102, 102, 255);   // 狙击枪 - 深青色（隐蔽联想）
    imguiswitch_information.colorswitch[20] = ImColor(128, 128, 128, 255); // 其他枪械 - 中灰色（中性色）
    imguiswitch_information.colorswitch[21] = ImColor(204, 51, 51, 255);   // 步配颜色 - 暗橙红（配件专用）
    imguiswitch_information.colorswitch[22] = ImColor(102, 0, 204, 255);   // 倍镜颜色 - 蓝紫色（光学联想）
    imguiswitch_information.colorswitch[23] = ImColor(77, 77, 0, 255);     // 地铁颜色 - 橄榄绿（与载具纯绿截然不同）
    std::thread([this]()
                {
        std::this_thread::sleep_for(std::chrono::milliseconds(100)); // 延时300毫秒
        touch_information.TouchOrientationControl = true; })
        .detach();
}
VecTor2 ImGuiELGS::GetTouchScreenDimension(int Handle)
{
    int TouchScreenX[6] = {0};
    int TouchScreenY[6] = {0};
    ioctl(Handle, EVIOCGABS(ABS_MT_POSITION_X), TouchScreenX);
    ioctl(Handle, EVIOCGABS(ABS_MT_POSITION_Y), TouchScreenY);
    return VecTor2(TouchScreenX[2], TouchScreenY[2]);
}

ImTextureID ImGuiELGS::texturereadsfile(const unsigned char *buffer, int length)
{
    int w = 0, h = 0, n = 0;
    GLuint texture;

    // 加载图片数据
    stbi_uc *bufferdata = stbi_png_load_from_memory(buffer, length, &w, &h, &n, 0);
    if (!bufferdata)
    {
        return nullptr;
    }

    // 生成纹理ID并设置纹理参数
    glGenTextures(1, &texture);
    glEnable(GL_TEXTURE_2D);
    glBindTexture(GL_TEXTURE_2D, texture);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_NEAREST);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_NEAREST);

    // 根据通道数判断是否为灰度、RGB或RGBA
    if (n == 1)
    {
        glTexImage2D(GL_TEXTURE_2D, 0, GL_LUMINANCE, w, h, 0, GL_LUMINANCE, GL_UNSIGNED_BYTE, bufferdata);
    }
    else if (n == 3)
    {
        glTexImage2D(GL_TEXTURE_2D, 0, GL_RGB, w, h, 0, GL_RGB, GL_UNSIGNED_BYTE, bufferdata);
    }
    else if (n == 4)
    {
        glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, w, h, 0, GL_RGBA, GL_UNSIGNED_BYTE, bufferdata);
    }
    else
    {
        stbi_image_free(bufferdata);
        return nullptr;
    }

    // 释放图像数据
    stbi_image_free(bufferdata);

    // 返回生成的纹理ID
    return reinterpret_cast<ImTextureID>(static_cast<uintptr_t>(texture));
}

string ImGuiELGS::GetTouchScreenDeviceFile()
{
    const char *command = "getevent";
    FILE *PIPE = popen(command, "r");
    if (!PIPE)
    {
        return "";
    }
    int lineCount = 0;
    string result = "";
    char buffer[128] = "";
    cout << "\033[35;1m[+] 请滑动一下屏幕\033[30;1m" << endl;
    while (!feof(PIPE))
    {
        if (fgets(buffer, 128, PIPE) != NULL)
        {
            string line(buffer);
            if (line.find("/dev/input/event") != string::npos)
            {
                result += line;
                lineCount++;
            }
            if (lineCount == 20)
            {
                break;
            }
        }
    }
    pclose(PIPE);
    regex pattern("/dev/input/event\\d+");
    sregex_iterator iter(result.begin(), result.end(), pattern);
    sregex_iterator end;
    string lastEventLine = "";
    while (iter != end)
    {
        lastEventLine = iter->str();
        ++iter;
    }
    return lastEventLine;
}

bool ImGuiELGS::ImGuiGetSurfaceWindow()
{
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    if (output == 1)
    {
        native_window = Android::NativeWindowCreator::CreateSurfaceNativeWindow("Canary", resolution_information.FixedScreenWidth + resolution_information.FixedScreenHeiht, resolution_information.FixedScreenHeiht + resolution_information.FixedScreenWidth, 0x40);
    }
    else if (output == 2)
    {
        native_window = Android::NativeWindowCreator::CreateSurfaceNativeWindow("Canary", resolution_information.FixedScreenWidth + resolution_information.FixedScreenHeiht, resolution_information.FixedScreenHeiht + resolution_information.FixedScreenWidth, 0x00);
    }
    else
    {
        cout << "\033[31;1m[+] 输入错误\033[30;1m" << endl;
        return false;
    }
    ANativeWindow_acquire(native_window);
    display = eglGetDisplay(EGL_DEFAULT_DISPLAY);
    if (display == EGL_NO_DISPLAY)
    {
        return false;
    }
    if (eglInitialize(display, 0, 0) != EGL_TRUE)
    {
        return false;
    }
    EGLint num_config = 0;
    const EGLint attribList[] = {EGL_SURFACE_TYPE, EGL_WINDOW_BIT, EGL_RENDERABLE_TYPE, EGL_OPENGL_ES2_BIT, EGL_BLUE_SIZE, 8, EGL_GREEN_SIZE, 8, EGL_RED_SIZE, 8, EGL_ALPHA_SIZE, 8, EGL_BUFFER_SIZE, 32, EGL_DEPTH_SIZE, 24, EGL_STENCIL_SIZE, 8, EGL_NONE};
    if (eglChooseConfig(display, attribList, nullptr, 0, &num_config) != EGL_TRUE)
    {
        return false;
    }
    if (!eglChooseConfig(display, attribList, &config, 1, &num_config))
    {
        return false;
    }
    EGLint egl_format;
    eglGetConfigAttrib(display, config, EGL_NATIVE_VISUAL_ID, &egl_format);
    ANativeWindow_setBuffersGeometry(native_window, 0, 0, egl_format);
    const EGLint attrib_list[] = {EGL_CONTEXT_CLIENT_VERSION, 3, EGL_NONE};
    context = eglCreateContext(display, config, EGL_NO_CONTEXT, attrib_list);
    if (context == EGL_NO_CONTEXT)
    {
        return false;
    }
    surface = eglCreateWindowSurface(display, config, native_window, nullptr);
    if (surface == EGL_NO_SURFACE)
    {
        return false;
    }
    if (!eglMakeCurrent(display, surface, surface, context))
    {
        return false;
    }
    return true;
}

void ImGuiELGS::ImGuiGetScreenInformation()
{
    touch_information.TouchDeviceFile = open(GetTouchScreenDeviceFile().c_str(), O_RDONLY | O_SYNC | O_NONBLOCK);
    if (touch_information.TouchDeviceFile <= 0)
    {
        cout << "\033[31;1m[-] 获取触摸设备文件失败\033[30;1m" << endl;
        return;
    }
    else
    {
        touch_information.TouchScreenSize = GetTouchScreenDimension(touch_information.TouchDeviceFile);
    }
    thread *screeninformationthread = new thread([this]
                                                 {
		static int ORIENTATIONTEMP = 0;
        for (;;) {
			displayinformation = Android::NativeWindowCreator::GetDisplayInfo();
			if (!ORIENTATIONTEMP) {
				resolution_information.Orientation = displayinformation.orientation;
				ORIENTATIONTEMP = 1;
			}
			resolution_information.Width = displayinformation.upwidth / 2;
			resolution_information.Heiht = displayinformation.upheight / 2;
			resolution_information.ScreenWidth = displayinformation.upwidth;
			resolution_information.ScreenHeiht = displayinformation.upheight;
			resolution_information.FixedScreenWidth = displayinformation.width;
			resolution_information.FixedScreenHeiht = displayinformation.height;
			if (displayinformation.orientation != resolution_information.Orientation) {
				touch_information.TouchOrientationControl = true;
				resolution_information.Orientation = displayinformation.orientation;
			}
			this_thread::sleep_for(0.1s);
        } });
    screeninformationthread->detach();
    return;
}

void ImGuiELGS::ImGuiWindowStar()
{
    ImGui_ImplOpenGL3_NewFrame();
    ImGui_ImplAndroid_NewFrame();
    ImGui::NewFrame();
}

void ImGuiELGS::ImGuiWindowExit()
{
    glViewport(0, 0, ImGui::GetIO().DisplaySize.x, ImGui::GetIO().DisplaySize.y);
    glClearColor(0, 0, 0, 0);
    glClear(GL_COLOR_BUFFER_BIT);
    glFlush();
    if (display == EGL_NO_DISPLAY)
    {
        return;
    }
    ImGui::Render();
    ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());
    eglSwapBuffers(display, surface);
}

void ImGuiELGS::ImGuiWindowRele()
{
    ImGui_ImplOpenGL3_Shutdown();
    ImGui_ImplAndroid_Shutdown();
    ImGui::DestroyContext();
    if (display != EGL_NO_DISPLAY)
    {
        eglMakeCurrent(display, EGL_NO_SURFACE, EGL_NO_SURFACE, EGL_NO_CONTEXT);
        if (context != EGL_NO_CONTEXT)
        {
            eglDestroyContext(display, context);
        }
        if (surface != EGL_NO_SURFACE)
        {
            eglDestroySurface(display, surface);
        }
        eglTerminate(display);
    }
    display = EGL_NO_DISPLAY;
    context = EGL_NO_CONTEXT;
    surface = EGL_NO_SURFACE;
    ANativeWindow_release(native_window);
}

void ImGuiELGS::ImGuiInItialization()
{
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO &io = ImGui::GetIO();
    io.IniFilename = nullptr; // 禁用 .ini 文件保存
    io.FontGlobalScale = 1.2f;
    // 使用亮色主题并自定义样式
    ImGui::StyleColorsLight();
    ImGuiStyle &style = ImGui::GetStyle();

    // 设置圆角
    style.WindowRounding = 12.0f;    // 窗口圆角
    style.ChildRounding = 12.0f;     // 子窗口圆角
    style.FrameRounding = 12.0f;     // 框架圆角
    style.PopupRounding = 12.0f;     // 弹出窗口圆角
    style.ScrollbarRounding = 12.0f; // 滚动条圆角
    style.GrabRounding = 12.0f;      // 滑块圆角
    style.TabRounding = 12.0f;       // 标签页圆角

    // 增大滑块大小
    style.GrabMinSize = 30.0f;   // 滑块最小大小
    style.ScrollbarSize = 40.0f; // 滚动条大小

    // 调整边距和间距
    style.WindowPadding = ImVec2(15, 15);
    style.FramePadding = ImVec2(8, 8);
    style.ItemSpacing = ImVec2(10, 8);
    style.ItemInnerSpacing = ImVec2(8, 6);

    // 设置边框
    style.FrameBorderSize = 0.0f;
    style.WindowBorderSize = 1.0f;

    // 设置颜色
    auto &colors = style.Colors;
    colors[ImGuiCol_WindowBg] = ImVec4(0.94f, 0.94f, 0.94f, 1.00f);
    colors[ImGuiCol_FrameBg] = ImVec4(0.85f, 0.85f, 0.85f, 1.00f);
    colors[ImGuiCol_FrameBgHovered] = ImVec4(0.80f, 0.80f, 0.80f, 1.00f);
    colors[ImGuiCol_FrameBgActive] = ImVec4(0.75f, 0.75f, 0.75f, 1.00f);
    colors[ImGuiCol_SliderGrab] = ImVec4(0.54f, 0.54f, 0.54f, 1.00f);
    colors[ImGuiCol_SliderGrabActive] = ImVec4(0.44f, 0.44f, 0.44f, 1.00f);
    colors[ImGuiCol_Button] = ImVec4(0.85f, 0.85f, 0.85f, 1.00f);
    colors[ImGuiCol_ButtonHovered] = ImVec4(0.80f, 0.80f, 0.80f, 1.00f);
    colors[ImGuiCol_ButtonActive] = ImVec4(0.75f, 0.75f, 0.75f, 1.00f);
    colors[ImGuiCol_Header] = ImVec4(0.85f, 0.85f, 0.85f, 1.00f);
    colors[ImGuiCol_HeaderHovered] = ImVec4(0.80f, 0.80f, 0.80f, 1.00f);
    colors[ImGuiCol_HeaderActive] = ImVec4(0.75f, 0.75f, 0.75f, 1.00f);
    colors[ImGuiCol_Border] = ImVec4(0.60f, 0.60f, 0.60f, 0.50f);
    colors[ImGuiCol_ScrollbarBg] = ImVec4(0.85f, 0.85f, 0.85f, 1.00f);
    colors[ImGuiCol_ScrollbarGrab] = ImVec4(0.65f, 0.65f, 0.65f, 1.00f);
    colors[ImGuiCol_ScrollbarGrabHovered] = ImVec4(0.55f, 0.55f, 0.55f, 1.00f);
    colors[ImGuiCol_ScrollbarGrabActive] = ImVec4(0.45f, 0.45f, 0.45f, 1.00f);
    // 其他初始化
    InitializeFoundationConfiGuration();
    ImGui_ImplAndroid_Init(native_window);
    ImGui_ImplOpenGL3_Init("#version 300 es");

    // 字体加载
    io.Fonts->AddFontFromMemoryTTF((void *)FontFile, Fontsize, 24.f, NULL, io.Fonts->GetGlyphRangesChineseFull());
    io.Fonts->AddFontFromMemoryTTF((void *)icons_binary, sizeof(icons_binary), 28.f);
    io.Fonts->AddFontFromMemoryTTF((void *)font_bold_binary, sizeof(font_bold_binary), 28.f);
    IM_ASSERT(io.Fonts != NULL);

    LoadFile(".Canary");
    加载图片();
}

bool ImGuiELGS::InitializeDrawing()
{
    GetPid("com.tencent.tmgp.pubgmhd");
    // KMA驱动初始化现在在GetPid中自动完成
    ModulesBase[0] = GetModuleAddressTwo("libUE4.so");
    // printf("base = %lX\n", ModulesBase[0]);
    return true;
}
