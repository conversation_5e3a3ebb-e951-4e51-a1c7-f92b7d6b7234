{"tasks": [{"type": "shell", "label": "NDK Build", "command": "C:\\Android\\SDK\\ndk\\27.2.12479018\\ndk-build.cmd", "args": [], "options": {"cwd": "${workspaceFolder}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gcc"], "detail": "运行NDK编译命令"}, {"type": "cppbuild", "label": "C/C++: clang++.exe 生成活动文件", "command": "C:\\Android\\SDK\\ndk\\27.2.12479018\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe", "args": ["-fcolor-diagnostics", "-fansi-escape-codes", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": "build", "detail": "调试器生成的任务。"}, {"type": "cppbuild", "label": "C/C++: clang-cl.exe 生成活动文件", "command": "C:\\Android\\SDK\\ndk\\27.2.12479018\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang-cl.exe", "args": ["-fcolor-diagnostics", "-fansi-escape-codes", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "调试器生成的任务。"}], "version": "2.0.0"}